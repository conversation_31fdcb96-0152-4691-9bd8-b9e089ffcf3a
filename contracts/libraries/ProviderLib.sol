// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library ProviderLib {
    bytes32 private constant _EMPTY_BYTES32 = 0x00;
    uint16 private constant _EMPTY_UINT16 = 0;
    uint256 private constant _EMPTY_LENGTH = 0;

    /**
     * @dev ゾーン情報の取得
     *
     * @param providerMapping プロバイダデータを保存するマッピング
     * @param zoneDataMapping ゾーンデータを保存するマッピング
     * @param providerId マッピングのキーとなるproviderId
     * @return zoneId providerが属するZoneのId
     * @return zoneName Zoneの名称
     * @return err エラーメッセージ
     */
    function getZone(
        mapping(bytes32 => ProviderData) storage providerMapping,
        mapping(uint16 => ZoneData) storage zoneDataMapping,
        bytes32 providerId
    )
        external
        view
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        if (providerId == _EMPTY_BYTES32) {
            // TODO: Refactor時に_EMPTY_xxxに修正
            return (0, "", Error.GE0102_PROV_NOT_EXIST);
        }
        if (providerMapping[providerId].zoneId == 0) {
            // TODO: Refactor時に_EMPTY_xxxに修正
            return (0, "", Error.GE0103_ZONE_NOT_EXIST);
        }
        return (
            providerMapping[providerId].zoneId,
            zoneDataMapping[providerMapping[providerId].zoneId].zoneName,
            ""
        );
    }

    /**
     * @dev マッピングデータへのProviderDataの追加。
     *
     * @param providerMapping プロバイダデータを保存するマッピング
     * @param zoneDataMapping ゾーンデータを保存するマッピング
     * @param providerId マッピングのキーとなるproviderId
     * @param role providerのロール
     * @param zoneId providerが属するZoneのId
     * @param zoneName providerが属するZoneの名称
     */
    function addProvider(
        mapping(bytes32 => ProviderData) storage providerMapping,
        mapping(uint16 => ZoneData) storage zoneDataMapping,
        bytes32 providerId,
        bytes32 role,
        uint16 zoneId,
        string memory zoneName
    ) external {
        providerMapping[providerId].role = role;
        providerMapping[providerId].zoneId = zoneId;
        providerMapping[providerId].enabled = true;

        zoneDataMapping[zoneId].zoneId = zoneId;
        zoneDataMapping[zoneId].zoneName = zoneName;
    }

    /**
     * @dev マッピングデータのproviderの名前の編集。
     *
     * @param providerMapping プロバイダデータを保存するマッピング
     * @param providerId マッピングのキーとなるproviderId
     * @param name providerの名前
     */
    function modProviderName(
        mapping(bytes32 => ProviderData) storage providerMapping,
        bytes32 providerId,
        bytes32 name
    ) external {
        if (name != _EMPTY_BYTES32) {
            providerMapping[providerId].name = name;
        }
    }

    /**
     * @dev Zone名の更新。
     *
     * @param providerMapping プロバイダデータを保存するマッピング
     * @param providerId マッピングのキーとなるproviderId
     * @param zoneName 更新後のzone名
     */
    function modZoneName(
        mapping(bytes32 => ProviderData) storage providerMapping,
        mapping(uint16 => ZoneData) storage zoneDataMapping,
        bytes32 providerId,
        string memory zoneName
    ) external {
        uint16 providerZoneId = providerMapping[providerId].zoneId;
        zoneDataMapping[providerZoneId].zoneName = zoneName;
    }

    /**
     * @dev Providerの存在確認。
     *
     * @param storedProviderId マッピングのキーとなるproviderId
     * @param inputProviderId チェック対象のproviderId
     * @return success true:Providerが存在する,false:Providerが存在しない
     * @return err エラーメッセージ
     */
    function hasProvider(bytes32 storedProviderId, bytes32 inputProviderId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (inputProviderId == _EMPTY_BYTES32) {
            return (false, Error.RV0003_PROV_INVALID_VAL);
        }
        if (storedProviderId == inputProviderId) {
            return (true, "");
        }
        return (false, Error.GE0101_PROV_ID_NOT_EXIST);
    }
}
