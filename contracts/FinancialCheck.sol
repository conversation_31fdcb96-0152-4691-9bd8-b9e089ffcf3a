// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/ITransferable.sol";
import "./interfaces/Error.sol";
import "./ValidatorLogic.sol";
import "./libraries/TokenLib.sol";
import "./interfaces/Struct.sol";

contract FinancialCheck is Initializable, IFinancialCheck {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /* @dev issueVoucherの計算パターン */
    uint256 private constant CALC_ISSUE = 1;
    /* @dev redeemVoucherの計算パターン */
    uint256 private constant CALC_REDEEM = 2;
    /* @dev 未登録の場合にて返す空の値 */
    bytes32 private constant _EMPTY_VALUE = 0x00;
    /* @dev AccountStatus判定用の定数(口座申し込み) */
    bytes32 private constant _STATUS_APPLIYNG = "applying";
    /* @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 private constant _STATUS_TERMINATING = "terminating";
    /* @dev 累積限度額の加算を行わない */
    bool private constant NOT_ADD_CUMULATIVE = false;
    /* @dev 累積限度額の加算を行う */
    bool private constant ADD_CUMULATIVE = true;
    /** @dev 口座の開設状況が初期化済みであることを表す固定値 **/
    uint256 private constant STATE_CODE_INITIALIZED = 0;
    /* @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_TRANSFER = "transfer";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_SYNC = "synchronous";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_EXCHANGE = "exchange";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_APPROVE = "approve";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _EMPTY_BYTES65_HASH = keccak256(abi.encodePacked(new bytes(65)));
    bytes32 private constant _EMPTY_BYTES512_HASH = keccak256(abi.encodePacked(new bytes(512)));

    // アカウントタイプ
    enum AccountType {
        SEND,
        FROM,
        TO
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev アカウントの有効性確認
     * @param accountId アカウントID
     * @param accountType アカウントタイプ
     * @return success アカウントが有効かどうか
     * @return err エラーメッセージ
     */
    function _checkAccountActivation(bytes32 accountId, AccountType accountType)
        internal
        view
        returns (bool success, string memory err)
    {
        (success, err) = _contractManager.account().isActivated(accountId);
        if (!success) {
            bytes32 errHash = keccak256(abi.encodePacked(err));

            if (errHash == keccak256(abi.encodePacked(Error.RV0007_ACCOUNT_INVALID_VAL))) {
                if (accountType == AccountType.SEND)
                    return (false, Error.SEND_ACCOUNT_IS_INVALID_VALUE);
                if (accountType == AccountType.FROM)
                    return (false, Error.FROM_ACCOUNT_IS_INVALID_VALUE);
                if (accountType == AccountType.TO)
                    return (false, Error.TO_ACCOUNT_IS_INVALID_VALUE);
            } else if (errHash == keccak256(abi.encodePacked(Error.GE0105_ACCOUNT_ID_NOT_EXIST))) {
                if (accountType == AccountType.SEND)
                    return (false, Error.SEND_ACCOUNT_IS_NOT_EXIST);
                if (accountType == AccountType.FROM)
                    return (false, Error.FROM_ACCOUNT_IS_NOT_EXIST);
                if (accountType == AccountType.TO) return (false, Error.TO_ACCOUNT_IS_NOT_EXIST);
            } else if (errHash == keccak256(abi.encodePacked(Error.GE2005_ACCOUNT_DISABLED))) {
                if (accountType == AccountType.SEND)
                    return (false, Error.SEND_ACCOUNT_STATUS_IS_DISABLED);
                if (accountType == AccountType.FROM)
                    return (false, Error.FROM_ACCOUNT_STATUS_IS_DISABLED);
                if (accountType == AccountType.TO)
                    return (false, Error.TO_ACCOUNT_STATUS_IS_DISABLED);
            }
        }
        return (true, "");
    }

    /**
     * @dev BizZoneのアカウント有効性確認
     * @param zoneId 処理ゾーンのID
     * @param accountId アカウントID
     * @param accountType アカウントタイプ
     * @return success アカウントが有効かどうか
     * @return err エラーメッセージ
     */
    function _checkBizAccountActivationByZone(
        uint16 zoneId,
        bytes32 accountId,
        AccountType accountType
    ) internal view returns (bool success, string memory err) {
        (success, err) = _contractManager.businessZoneAccount().isActivatedByZone(
            zoneId,
            accountId
        );
        if (!success) {
            bytes32 errHash = keccak256(abi.encodePacked(err));
            if (errHash == keccak256(abi.encodePacked(Error.GE0105_ACCOUNT_ID_NOT_EXIST))) {
                if (accountType == AccountType.SEND)
                    return (false, Error.SEND_ACCOUNT_IS_NOT_EXIST);
                if (accountType == AccountType.FROM)
                    return (false, Error.FROM_ACCOUNT_IS_NOT_EXIST);
                if (accountType == AccountType.TO) return (false, Error.TO_ACCOUNT_IS_NOT_EXIST);
            } else if (errHash == keccak256(abi.encodePacked(Error.GE2005_ACCOUNT_DISABLED))) {
                if (accountType == AccountType.SEND)
                    return (false, Error.SEND_ACCOUNT_STATUS_IS_DISABLED);
                if (accountType == AccountType.FROM)
                    return (false, Error.FROM_ACCOUNT_STATUS_IS_DISABLED);
                if (accountType == AccountType.TO)
                    return (false, Error.TO_ACCOUNT_STATUS_IS_DISABLED);
            }
        }
        return (true, "");
    }

    /**
     * @notice FinancialZoneトランザクションをチェックする
     * @dev この関数はFinDLT上で実行されるためBizDZone実行時には参照先を意識して呼び出す必要がある
     * @param zoneId 処理ゾーンのID
     * @param sendAccountId 送信元アカウントID
     * @param fromAccountId 送金元アカウントID
     * @param toAccountId 送金先アカウントID
     * @param fromAccountIssuerId 送金元アカウントのイシュアーID
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param accountSignature アカウント署名
     * @param info 追加情報
     * @return success トランザクションが成功したかどうか
     * @return err エラーメッセージ
     */
    function checkTransaction(
        uint16 zoneId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 fromAccountIssuerId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        bytes memory accountSignature,
        bytes memory info
    ) external view returns (bool success, string memory err) {
        // 共通領域でのSendAccountの有効性を確認する
        {
            // SendAccount有効性確認
            (success, err) = _checkAccountActivation(sendAccountId, AccountType.SEND);
            if (!success) return (false, err);
        }
        // 共通領域でのFromAccountの有効性を確認する
        {
            // FromAccount有効性確認
            (success, err) = _checkAccountActivation(fromAccountId, AccountType.FROM);
            if (!success) return (false, err);
        }
        // 共通領域でのToAccountの有効性を確認する
        {
            // ToAccount有効性確認
            (success, err) = _checkAccountActivation(toAccountId, AccountType.TO);
            if (!success) return (false, err);
        }
        {
            // fromAccountIdとtoAccountIdが同じの場合にエラー
            if (fromAccountId == toAccountId) {
                return (false, Error.UE2005_FROM_TO_SAME);
            }
            // 送金先アカウントIDが送金元のイシュアーIDに属するか確認
            (success, err) = _contractManager.issuer().hasAccount(fromAccountIssuerId, toAccountId);
            if (!success) {
                return (false, Error.FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT);
            }

            // accountSignatureとsigInfoが共に0でない場合のみアカウント署名検証を行う　// TODO: 恒久対応として_sigVeruifyを削除する
            if (
                keccak256(accountSignature) != _EMPTY_BYTES65_HASH &&
                keccak256(info) != _EMPTY_BYTES512_HASH
            ) {
                // Accountの署名検証に使用するHashを生成する
                bytes32 hashAccount = keccak256(
                    abi.encode(sendAccountId, fromAccountId, toAccountId, amount, _STRING_TRANSFER)
                );
                (success, err) = _contractManager.accessCtrl().checkSigAccount(
                    sendAccountId,
                    hashAccount,
                    accountSignature,
                    info
                );
                if (!success) {
                    return (success, err);
                }
            }
        }
        {
            // FinZoneからのリクエストである場合、FinZoneの送金元アカウントの残高確認を行う
            // BizZoneからのリクエストである場合、BizZoneでの各アカウント有効性の確認、残高チェックを行う
            if (zoneId == _FINANCIAL_ZONE) {
                (AccountDataWithoutZoneId memory data, ) = _contractManager.account().getAccount(
                    fromAccountId
                );
                if (data.balance < amount) {
                    return (false, Error.UE4402_BALANCE_NOT_ENOUGH);
                }
            } else {
                // BizZoneでのSendAccountの有効性確認
                (success, err) = _checkBizAccountActivationByZone(
                    zoneId,
                    sendAccountId,
                    AccountType.SEND
                );
                if (!success) return (false, err);

                // BizZoneでのFromAccountの有効性確認
                (success, err) = _checkBizAccountActivationByZone(
                    zoneId,
                    fromAccountId,
                    AccountType.FROM
                );
                if (!success) return (false, err);

                // BizZoneでのToAccountの有効性確認
                (success, err) = _checkBizAccountActivationByZone(
                    zoneId,
                    toAccountId,
                    AccountType.TO
                );

                if (!success) return (false, err);
                // BizZoneでのFromAccountの残高確認
                BusinessZoneAccountData memory data = _contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(zoneId, fromAccountId);
                if (data.balance < amount) {
                    return (false, Error.UE4402_BALANCE_NOT_ENOUGH);
                }
            }
        }
        {
            // TransferLimitとDailyLimitを確認する
            (success, err) = _contractManager.financialZoneAccount().checkTransfer(
                fromAccountId,
                amount
            );
            if (!success) {
                return (success, err);
            }
        }
        {
            // miscValue2のサイズが4KB未満であることを確認する
            if (bytes(miscValue2).length > 4096) {
                return (false, Error.RV0008_TOKEN_INVALID_VAL);
            }
        }
        return (true, "");
    }

    // Exchange用の検証用関数
    function checkExchange(
        bytes32 accountId,
        uint16 fromZoneId,
        uint16 toZoneId,
        uint256 amount
    ) external view returns (bool success, string memory err) {
        if (fromZoneId == toZoneId) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }

        BusinessZoneAccountData memory data;
        // FromZoneが"Financial_zone"ではない場合はbalanceByZoneの残高とステータス確認を行う
        if (fromZoneId != _FINANCIAL_ZONE) {
            // fromZoneIdでアカウント情報を取得する
            data = _contractManager.businessZoneAccount().getBusinessZoneAccount(
                fromZoneId,
                accountId
            );
            if (data.accountStatus != Constant._STATUS_ACTIVE) {
                return (false, Error.ACCOUNT_STATUS_INVALID);
            }
            if (data.balance < amount) {
                return (false, Error.UE4402_BALANCE_NOT_ENOUGH);
            }
            // fromZoneIdがFinZoneではないことから、dischargeの処理と判定し、checkDischargeを呼び出す
            return _contractManager.financialZoneAccount().checkDischarge(accountId, amount);
        } else {
            // toZoneIdでアカウント情報を取得する
            data = _contractManager.businessZoneAccount().getBusinessZoneAccount(
                toZoneId,
                accountId
            );
            if (data.accountStatus != Constant._STATUS_ACTIVE) {
                return (false, Error.ACCOUNT_STATUS_INVALID);
            }
            // fromZoneIdがFinZoneであることから、chargeの処理と判定し、checkChargeを呼び出す
            return _contractManager.financialZoneAccount().checkCharge(accountId, amount);
        }
    }

    // SyncAccountの検証用関数
    function checkSyncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        uint16 zoneId,
        bytes32 accountStatus,
        bytes memory accountSignature,
        bytes memory info
    ) external view override returns (bool success, string memory err) {
        // Validator未入力チェック
        if (validatorId == _EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        // アカウントステータスの入力チェック
        if (accountStatus != _STATUS_APPLIYNG && accountStatus != _STATUS_TERMINATING) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }

        // 認可イシュアを確認
        // 通常はCoreが認可イシュアを利用する為、エラーが発生しない
        (success, err) = _contractManager.provider().checkAvailableIssuerIds(zoneId, accountId);
        if (success == false) {
            return (false, err);
        }

        // inputするアカウントステータスがterminatingの場合はbusiness zoneのアカウント存在チェック
        if (accountStatus == Constant._STATUS_TERMINATING) {
            // BizAccount存在確認
            (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(
                zoneId,
                accountId
            );
            if (!success) {
                return (false, err);
            }

            // BizAccount状態確認
            BusinessZoneAccountData memory data = _contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneId, accountId);
            if (
                data.accountStatus == Constant._STATUS_TERMINATING ||
                data.accountStatus == Constant._STATUS_TERMINATED
            ) {
                return (false, Error.ACCOUNT_TERMINATING_OR_TERINATED);
            }
            if (data.balance != 0) {
                return (false, Error.ACCOUNT_BALANCE_NOT_ZERO);
            }
        } else if (accountStatus == Constant._STATUS_APPLIYNG) {
            // BizAccount未存在確認
            (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(
                zoneId,
                accountId
            );
            if (success) {
                // 存在する場合、BizAccount状態強制償却済みであることを確認
                BusinessZoneAccountData memory data = _contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(zoneId, accountId);
                if (data.accountStatus != Constant._STATUS_FORCE_BURNED) {
                    return (false, Error.ACCOUNT_NOT_FORCE_BURNED);
                }
            }
        }

        // Accountの署名検証に使用するHashを生成する
        bytes32 hashAccount = keccak256(abi.encode(accountId, _STRING_SYNC));
        // Accountの署名を検証する
        (success, err) = _contractManager.accessCtrl().checkSigAccount(
            accountId,
            hashAccount,
            accountSignature,
            info
        );
        if (success == false) {
            return (false, err);
        }
        return (true, "");
    }

    // FinZoneのアカウント状態を参照するための関数(BizZoneからFinZoneの状態を参照)
    function checkFinAccountStatus(bytes32 accountId)
        external
        view
        override
        returns (bytes32 accountStatus, string memory err)
    {
        AccountDataWithoutZoneId memory accountData;
        (accountData, err) = _contractManager.account().getAccount(accountId);

        return (accountData.accountStatus, err);
    }

    /**
     * @dev BizZone側からFinZoneの限度額情報を参照するための関数(bizZoneのBCClientからのみ呼び出される)
     *
     * @param accountId accountId
     * @return accountLimitData accountLimitData
     *
     */
    function getAccountLimit(bytes32 accountId)
        external
        view
        returns (FinancialZoneAccountData memory accountLimitData, string memory err)
    {
        (bool success, string memory errAcc) = _contractManager.account().hasAccount(accountId);
        if (!success) {
            CumulativeTransactionLimits memory emptyLimits = CumulativeTransactionLimits({
                cumulativeMintLimit: 0,
                cumulativeMintAmount: 0,
                cumulativeBurnLimit: 0,
                cumulativeBurnAmount: 0,
                cumulativeChargeLimit: 0,
                cumulativeChargeAmount: 0,
                cumulativeDischargeLimit: 0,
                cumulativeDischargeAmount: 0,
                cumulativeTransferLimit: 0,
                cumulativeTransferAmount: 0
            });
            return (FinancialZoneAccountData(0, 0, 0, 0, 0, 0, 0, 0, emptyLimits), errAcc);
        }
        // Get account limit data from financial zone account
        (FinancialZoneAccountData memory data, ) = _contractManager
            .financialZoneAccount()
            .getAccountLimitData(accountId);
        // Get current JST day
        uint256 currentDay = _contractManager.financialZoneAccount().getJSTDay();

        // If cumulativeDate is before current day, reset all cumulative amounts in memory
        if (data.cumulativeDate < currentDay) {
            data.cumulativeAmount = Constant._RESET_CUMULATIVE_AMOUNT;
            data.cumulativeTransactionLimits.cumulativeMintAmount = Constant
                ._RESET_CUMULATIVE_AMOUNT;
            data.cumulativeTransactionLimits.cumulativeBurnAmount = Constant
                ._RESET_CUMULATIVE_AMOUNT;
            data.cumulativeTransactionLimits.cumulativeChargeAmount = Constant
                ._RESET_CUMULATIVE_AMOUNT;
            data.cumulativeTransactionLimits.cumulativeDischargeAmount = Constant
                ._RESET_CUMULATIVE_AMOUNT;
            data.cumulativeTransactionLimits.cumulativeTransferAmount = Constant
                ._RESET_CUMULATIVE_AMOUNT;
        }
        return (data, "");
    }

    /**
     * @dev BizZone側からFinZoneのアカウントステータスを参照するための関数(bizZoneのBCClientからのみ呼び出される)
     *
     * @param accountId accountId
     * @param zoneId accountId
     * @return accountStatus accountStatus
     *
     */
    function getBizZoneAccountStatus(bytes32 accountId, uint16 zoneId)
        external
        view
        returns (bytes32 accountStatus, string memory err)
    {
        if (zoneId == 0x00) {
            return (Constant._EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (accountId == 0x00) {
            return (Constant._EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!_contractManager.businessZoneAccount().accountIdExistenceByZoneId(zoneId, accountId)) {
            return (Constant._EMPTY_VALUE, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        bool success;
        (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(zoneId, accountId);
        if (!success) {
            return (Constant._EMPTY_VALUE, err);
        }

        BusinessZoneAccountData memory businessZoneAccountData = _contractManager
            .businessZoneAccount()
            .getBusinessZoneAccount(zoneId, accountId);
        return (businessZoneAccountData.accountStatus, "");
    }

    /**
     * @dev Biz認可イシュア一覧取得処理
     *
     * @param zoneId zoneId
     * @param offset offset
     * @param limit limit
     * @return issuers イシュアリストデータ
     * @return totalCount 合計件数
     * @return err エラー
     *
     */
    function getIssuerWithZone(
        uint16 zoneId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        )
    {
        bytes32[] memory issuerIds = _contractManager.provider().getAvailableIssuerIds(zoneId);
        if (limit == 0 || issuerIds.length == 0) {
            return (issuers, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (issuers, _EMPTY_LENGTH, Error.UE0102_ISSUER_TOO_LARGE_LIMIT);
        }
        if (offset >= issuerIds.length) {
            return (issuers, _EMPTY_LENGTH, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (issuerIds.length >= offset + limit) ? limit : issuerIds.length - offset;

        issuers = new IssuerListData[](size);
        for (uint256 i = 0; i < size; i++) {
            (string memory name, uint16 bankCode, ) = _contractManager.issuer().getIssuer(
                issuerIds[i + offset]
            );
            issuers[i].issuerId = issuerIds[i + offset];
            issuers[i].name = name;
            issuers[i].bankCode = bankCode;
        }
        return (issuers, issuerIds.length, "");
    }
}
