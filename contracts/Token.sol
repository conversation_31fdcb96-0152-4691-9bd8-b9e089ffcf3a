// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/ITransferable.sol";
import "./interfaces/Error.sol";
import "./ValidatorLogic.sol";
import "./libraries/TokenLib.sol";
import "./remigration/RemigrationLib.sol";
import "./interfaces/Struct.sol";

contract Token is Initializable, IToken, ITransferable {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using TokenLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev ContractManagerアドレス */
    IContractManager private _contractManager;
    /* @dev tokenId */
    bytes32 private _tokenId;
    /* @dev TokenData */
    mapping(bytes32 => TokenData) private _tokenData;

    /* @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /* @dev BizZoneのID */
    uint16 private constant _BUSINESS_ZONE = 3001;
    /* @dev issueVoucherの計算パターン */
    uint256 private constant _CALC_ISSUE = 1;
    /* @dev redeemVoucherの計算パターン */
    uint256 private constant _CALC_REDEEM = 2;
    /** @dev 口座の開設状況が初期化済みであることを表す固定値 **/
    uint256 private constant _STATE_CODE_INITIALIZED = 0;
    /** @dev getTokenAllのsignature検証用 **/
    string private constant _GET_TOKEN_ALL_SIGNATURE = "getTokenAll";
    /* @dev setTokenAllのsignature検証用 */
    string private constant _SET_TOKEN_ALL_SIGNATURE = "setTokenAll";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_APPROVE = "approve";
    /** @dev  IBC検証用のtimeoutHeightの値*/
    uint64 private constant _TIMEOUT_HEIGHT = 9999999999999999999;
    /** @dev パラメータの検証に使用する固定値 **/
    bytes32 private constant _EMPTY_BYTES32 = bytes32(new bytes(0));

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Providerから呼ばれるaddToken。
     *
     * ```
     * emit event: AddToken()
     * ```
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol symbol
     * @param traceId トレースID
     */
    function addToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external override {
        //PROVIDERコントラクトからの呼び出しのみ
        require(
            msg.sender == address(_contractManager.provider()),
            Error.GA0005_NOT_PROVIDER_CONTRACT
        );

        _tokenData.addToken(tokenId, _tokenId, name, symbol);
        _tokenId = tokenId;

        (uint16 zoneId, string memory zoneName, string memory errZone) = _contractManager
            .provider()
            .getZone();
        require(bytes(errZone).length == 0, errZone);

        emit AddToken(tokenId, zoneId, zoneName, true, traceId);
    }

    /**
     * @dev Tokenの修正。名前、simbol、peg通貨の種類を修正できる。
     *
     * ```
     * emit event: ModToken()
     * ```
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol zoneId
     * @param traceId トレースID
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external override {
        //PROVIDERコントラクトからの呼び出しのみ
        require(
            msg.sender == address(_contractManager.provider()),
            Error.GA0005_NOT_PROVIDER_CONTRACT
        );

        _tokenData.modToken(tokenId, _tokenId, name, symbol);
        (name, symbol, , , ) = _tokenData.getToken(_tokenId);
        emit ModToken(tokenId, name, symbol, traceId);
    }

    /**
     * @dev Tokenのステータスを変更する。
     *
     * ```
     * emit event: SetEnabledToken()
     * ```
     *
     * @param providerId providerID
     * @param tokenId tokenId
     * @param enabled Tokenの有効性.true:有効,false:無効
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function setTokenEnabled(
        bytes32 providerId,
        bytes32 tokenId,
        bool enabled,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        //権限チェック
        {
            (bool success, string memory err) = _contractManager.provider().hasProvider(providerId);
            require(success, Error.GE0101_PROV_ID_NOT_EXIST);
            bytes32 hash = keccak256(abi.encode(providerId, tokenId, enabled, deadline));
            //権限チェック
            bool has;
            (has, err) = _contractManager.provider().checkRole(
                providerId,
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.GA0004_PROV_NOT_ROLE);
        }
        _tokenData.setTokenEnabled(tokenId, _tokenId, enabled);
        emit SetEnabledToken(tokenId, enabled, traceId);
    }

    /**
     * @dev 送金許可。
     *
     * ```
     * emit event: Approval()
     * ```
     * @param validatorId ownerId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金数量
     * @param traceId トレースID
     */
    function approve(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //存在チェック
        {
            (bool success, string memory err) = _contractManager.validator().hasValidator(
                validatorId
            );
            require(success, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        //Accountにてapproveを実行する
        _contractManager.account().approve(ownerId, spenderId, amount);

        emit Approval(validatorId, ownerId, spenderId, amount, traceId);
    }

    /**
     * @dev 発行。
     *
     * ```
     * emit event: Mint()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Mintする数量
     * @param traceId トレースID
     */
    function mint(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // Tokenの発行を行い、結果を保存(ライブラリ側で実行)
        (uint16 zoneId, bytes32 validatorId, string memory accountName, uint256 balance) = TokenLib
            .mint(address(_contractManager), _tokenData, _tokenId, issuerId, accountId, amount);

        // アカウントの限度額更新
        _contractManager.financialZoneAccount().syncMint(accountId, amount, traceId);

        emit Mint(zoneId, validatorId, issuerId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev 償却。
     *
     * ```
     * emit event: Burn()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function burn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // Tokenの償却を行い、結果を保存(ライブラリ側で実行)
        (uint16 zoneId, bytes32 validatorId, string memory accountName, uint256 balance) = TokenLib
            .burn(address(_contractManager), _tokenData, _tokenId, issuerId, accountId, amount);

        // アカウントの限度額更新
        _contractManager.financialZoneAccount().syncBurn(accountId, amount, traceId);

        emit Burn(zoneId, validatorId, issuerId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev 償却取り消し。
     *
     * ```
     * emit event: BurnCancel()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnを取り消す数量
     * @param blockTimestamp 取り消し対象のBurnの日時
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function burnCancel(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 blockTimestamp,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Tokenの償却取り消しを行い、結果を保存(ライブラリ側で実行)

        (uint16 zoneId, bytes32 validatorId, uint256 balance) = TokenLib.burnCancel(
            address(_contractManager),
            _tokenData,
            BurnCancelData({
                tokenId: _tokenId,
                issuerId: issuerId,
                accountId: accountId,
                amount: amount,
                blockTimestamp: blockTimestamp,
                deadline: deadline,
                signature: signature
            })
        );

        // Account already check in logic, remove duplicate check of get account to prevent stack too deep
        (AccountDataWithoutZoneId memory accountData, ) = _contractManager.account().getAccount(
            accountId
        );

        emit BurnCancel(
            zoneId,
            validatorId,
            issuerId,
            accountId,
            accountData.accountName,
            amount,
            balance,
            blockTimestamp,
            traceId
        );
    }

    /**
     * @dev 送金許可設定の取得 Callで呼ばれる時にValidatorの紐付き検証を行う。
     *
     * @param validatorId validatorId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @return allowance 許容額
     * @return approvedAt 許可日付
     * @return err エラーメッセージ
     */
    function getAllowance(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId
    )
        external
        view
        override
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        )
    {
        //OwnerID有効性チェック
        {
            (bool success, string memory errHas) = _contractManager.validator().hasAccount(
                validatorId,
                ownerId
            );
            if (!success) {
                return (0, 0, errHas);
            }
        }
        return _contractManager.account().getAllowance(ownerId, spenderId);
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     *
     * @param ownerAccountId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerAccountId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        override
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        return (_contractManager.account().getAllowanceList(ownerAccountId, offset, limit));
    }

    /**
     * @dev 単数のTransferを行う。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function transferSingle(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override {
        (uint16 zoneId, , ) = _contractManager.provider().getZone();
        if (zoneId == _FINANCIAL_ZONE) {
            // 実行箇所がFinZoneであればアカウントの限度額更新(BizZone実行の場合はIBCの伝播先で更新する)
            _contractManager.financialZoneAccount().syncTransfer(fromAccountId, amount, traceId);
        } else {
            // 実行箇所がBizZoneであればsyncTransferを呼び出してpacketをFinZoneに送信する
            _contractManager.balanceSyncBridge().syncTransfer(
                fromAccountId,
                toAccountId,
                zoneId,
                amount,
                _TIMEOUT_HEIGHT,
                traceId
            );
        }
        _transfer(
            sendAccountId,
            fromAccountId,
            toAccountId,
            amount,
            miscValue1,
            miscValue2,
            memo,
            traceId
        );
    }

    /**
     * @dev TotalSupplyを増額する。
     *
     * @param amount Mintする数量
     */
    function addTotalSupply(uint256 amount) external override {
        // Tokenの償却取り消しを行い、結果を保存(ライブラリ側で実行)
        _tokenData.addTotalSupply(_tokenId, amount);
    }

    /**
     * @dev TotalSupplyを減額する。
     *
     * @param amount Burnする数量
     */
    function subTotalSupply(uint256 amount) external override {
        // Tokenの償却取り消しを行い、結果を保存(ライブラリ側で実行)
        _tokenData.subTotalSupply(_tokenId, amount);
    }

    /**
     * @dev Transfer本体(内部関数)。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function _transfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) internal {
        // miscValueの値が0ではない場合はCustomTransferを行う
        if (miscValue1 != _EMPTY_BYTES32 || bytes(miscValue2).length != 0) {
            _contractManager.transferProxy().customTransfer(
                sendAccountId,
                fromAccountId,
                toAccountId,
                amount,
                miscValue1,
                miscValue2,
                memo,
                traceId
            );
        } else {
            TransferData memory data;
            {
                data.transferType = Constant._TRANSFER;
                data.bizZoneId = 0;
                data.sendAccountId = sendAccountId;
                data.fromAccountId = fromAccountId;
                data.toAccountId = toAccountId;
                data.amount = amount;
                data.memo = memo;
            }
            // 通常のTransferの場合はlocalTransfer()を実行する
            _localTransfer(data, traceId);
        }
    }

    /**
     * @dev カスタムコントラクトから呼び出す為のCustomTransfer。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     * @return result true:成功,false:失敗
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override returns (bool result) {
        TransferData memory data;
        {
            data.transferType = Constant._CUSTOM_TRANSFER;
            data.bizZoneId = 0;
            data.sendAccountId = sendAccountId;
            data.fromAccountId = fromAccountId;
            data.toAccountId = toAccountId;
            data.amount = amount;
            data.miscValue1 = miscValue1;
            data.miscValue2 = miscValue2;
            data.memo = memo;
        }
        _localTransfer(data, traceId);
        return true;
    }

    /**
     * @dev 送金(内部関数)。
     *
     * ```
     * emit event: Transfer()
     * ```
     *
     * @param data TransferData
     */
    function _localTransfer(TransferData memory data, bytes32 traceId) internal {
        TransferData memory emitData = TokenLib.transfer(address(_contractManager), data);

        emit Transfer(emitData, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(data.fromAccountId, data.toAccountId, traceId);
    }

    /**
     * @dev トークン全情報登録
     * @param token Tokenの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名

     */
    function setTokenAll(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_TOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(_err).length == 0, _err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);

        _tokenId = token.tokenId;
        RemigrationLib.setTokenAll(_tokenData, token);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev TokenId確認用。
     *
     * @param tokenId tokenId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasToken(bytes32 tokenId, bool chkEnabled)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasToken(tokenId, chkEnabled);
    }

    /**
     * @dev 内部管理TokenId確認用。
     *
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasTokenState() external view override returns (bool success, string memory err) {
        return _hasToken(_tokenId, true);
    }

    /**
     * @dev Token情報の取得。
     *
     * @return tokenId tokenId
     * @return name token名
     * @return symbol symbol
     * @return totalSupply tokenの総供給量
     * @return enabled ture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken()
        external
        view
        override
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        require(
            msg.sender == address(_contractManager.provider()),
            Error.GA0005_NOT_PROVIDER_CONTRACT
        );
        bool success;
        (success, err) = _hasToken(_tokenId, true);
        if (!success) {
            // TODO: Refactor時に_EMPTY_xxxに修正
            return (0x00, 0x00, 0x00, 0, false, Error.GE0107_TOKEN_NOT_EXIST);
        }
        (name, symbol, totalSupply, enabled, err) = _tokenData.getToken(_tokenId);
        return (_tokenId, name, symbol, totalSupply, enabled, err);
    }

    /**
     * @dev ビジネスゾーンの全残高情報を取得する
     *
     * @param accountId accountId
     * @return zoneIds ビジネスゾーンID
     * @return zoneNames ゾーン名
     * @return balances 残高
     * @return accountNames アカウント名
     * @return accountStatus アカウントステータス
     * @return totalBalance 合計残高
     * @return err エラーメッセージ
     */
    function getBalanceList(bytes32 accountId)
        external
        view
        returns (
            uint16[] memory zoneIds,
            string[] memory zoneNames,
            uint256[] memory balances,
            string[] memory accountNames,
            bytes32[] memory accountStatus,
            uint256 totalBalance,
            string memory err
        )
    {
        bool success;
        (success, err) = _contractManager.account().hasAccount(accountId);
        if (!success) {
            return (zoneIds, zoneNames, balances, accountNames, accountStatus, 0, err);
        }

        ZoneData[] memory zones = _contractManager.account().getZoneByAccountId(accountId);

        (zoneIds, zoneNames, balances, accountNames, accountStatus, totalBalance) = TokenLib
            .getBalanceList(address(_contractManager), accountId, zones);

        return (zoneIds, zoneNames, balances, accountNames, accountStatus, totalBalance, "");
    }

    /**
     * @dev Token有効性確認(内部関数)。
     *
     * @param tokenId tokenId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが有効,false:Tokenが無効
     * @return err エラーメッセージ
     */
    function _hasToken(bytes32 tokenId, bool chkEnabled)
        internal
        view
        returns (bool success, string memory err)
    {
        (success, err) = _tokenData.hasToken(_tokenId, tokenId, chkEnabled);
        return (success, err);
    }

    function checkApprove(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes memory accountSignature,
        bytes memory info,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        (success, err) = _contractManager.validator().hasValidator(validatorId);
        if (!success) {
            return (success, err);
        }
        // 最大限度額を超えている場合はエラー
        if (amount > Constant._MAX_ALLOWANCE_VALUE) {
            return (false, Error.ACCOUNT_EXCEED_APPROVAL_LIMIT);
        }
        // AccountとValidatorId紐付けられていない場合はエラー
        if (!_contractManager.validator().hasValidatorByAccount(validatorId, ownerId)) {
            return (false, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }
        {
            bytes32 hash = keccak256(abi.encode(validatorId, ownerId, deadline));
            //権限チェック
            bool has;
            (has, err) = _contractManager.validator().hasValidatorRole(
                validatorId,
                hash,
                deadline,
                signature
            );
            if (bytes(err).length != 0) {
                return (false, err);
            }
            if (!has) {
                return (false, Error.GA0018_VALIDATOR_NOT_ROLE);
            }
        }
        // Accountの署名検証に使用するHashを生成する
        bytes32 hashAccount = keccak256(abi.encode(ownerId, spenderId, amount, _STRING_APPROVE));
        // Accountの署名を検証する
        (success, err) = _contractManager.accessCtrl().checkSigAccount(
            ownerId,
            hashAccount,
            accountSignature,
            info
        );
        if (success == false) {
            return (success, err);
        }
        // Ownerの有効性確認
        {
            (success, err) = _contractManager.account().isActivated(ownerId);
            if (success == false) {
                return (success, err);
            }
        }
        // Spenderの有効性確認
        {
            (success, err) = _contractManager.account().isActivated(spenderId);
            if (success == false) {
                return (success, err);
            }
        }
        return (true, "");
    }

    /**
     * @dev Token全情報取得
     *      既に登録されているTokenの全情報を取得する
     *
     * @return token 全Tokenの情報
     */
    function getTokenAll() external view returns (TokenAll memory token) {
        return RemigrationLib.getTokenAll(_tokenData, _tokenId);
    }
}
