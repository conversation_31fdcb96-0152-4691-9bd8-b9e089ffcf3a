[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	hooksPath = .husky/_
[remote "origin"]
	url = **************:decurret-lab/dcbg-dcjpy-contract.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
	vscode-merge-base = origin/develop
[branch "feature/DCPF-41834-sync-limit-amount"]
	remote = origin
	merge = refs/heads/feature/DCPF-41834-sync-limit-amount
	vscode-merge-base = origin/feature/DCPF-41834-sync-limit-amount
[branch "story/DCPF-39105"]
	remote = origin
	vscode-merge-base = origin/story/DCPF-39105
	merge = refs/heads/story/DCPF-39105
[branch "feature/DCPF-39402"]
	remote = origin
	merge = refs/heads/feature/DCPF-39402
[branch "story/DCPF-39105-tmp"]
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
[branch "feature/DCPF-39402-tmp"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43807"]
	remote = origin
	merge = refs/heads/feature/DCPF-43807
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43807-bizzone-connectable-issusers"]
	remote = origin
	merge = refs/heads/feature/DCPF-43807-bizzone-connectable-issusers
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43930-account-information-batch"]
	remote = origin
	merge = refs/heads/feature/DCPF-43930-account-information-batch
[branch "feature/DCPF-43659-acccount-information-batch-tmp"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43659-acccount-information-batch"]
	vscode-merge-base = origin/develop
	remote = origin
	merge = refs/heads/feature/DCPF-43659-acccount-information-batch
[branch "feature/DCPF-43927-account-transaction-limit"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43658-partial-force-burn-tmp"]
	remote = origin
	merge = refs/heads/feature/DCPF-43658-partial-force-burn
[branch "feature/DCPF-43658-partial-force-burn"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43927-transaction-limit"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43928-transaction-limit"]
	vscode-merge-base = origin/develop
	remote = origin
	merge = refs/heads/feature/DCPF-43928-transaction-limit
[branch "feature/DCPF-43931-reset-transaction-limit"]
	remote = origin
	merge = refs/heads/feature/DCPF-43931-reset-transaction-limit
	vscode-merge-base = origin/develop
[branch "feature/DCPF-45715-backup-with-transaction-limit"]
	remote = origin
	merge = refs/heads/feature/DCPF-45715-backup-with-transaction-limit
	vscode-merge-base = origin/develop
[branch "feature/DCPF-45621-backup-restore-tmp"]
	remote = origin
	merge = refs/heads/feature/DCPF-45621-backup-restore
[branch "feature/DCPF-45621-backup-restore"]
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
	remote = origin
	merge = refs/heads/feature/DCPF-45621-backup-restore
[branch "feature/DCPF-45893-check-sync-account"]
	remote = origin
	merge = refs/heads/feature/DCPF-45893-check-sync-account
	vscode-merge-base = origin/develop
[branch "feature/DCPF-43372-update-documents"]
	remote = origin
	merge = refs/heads/feature/DCPF-43372-update-documents
[branch "feature/DCPF-45909-fix-nonce-manager"]
	remote = origin
	merge = refs/heads/feature/DCPF-45909-fix-nonce-manager
	vscode-merge-base = origin/develop
[branch "feature/DCPF-46706-backup-scenario"]
	remote = origin
	merge = refs/heads/feature/DCPF-46706-backup-scenario
	vscode-merge-base = origin/develop
[branch "fix/DCPF-50240/backup-scenario-spec"]
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
	remote = origin
	merge = refs/heads/fix/DCPF-50240/backup-scenario-spec
[branch "feature/DCPF-50854/setup-migration-script"]
	remote = origin
	merge = refs/heads/feature/DCPF-50854/setup-migration-script
	vscode-merge-base = origin/develop
[branch "feature/DCPF-50257/implement-migration-logic-mergeed"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-50257/implement-migration-logic-mergeed-2"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-51178/implement-verification-logic-ogirin"]
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
[branch "feature/DCPF-50911/implement-mapping-logic"]
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
[branch "feature/DCPF-51178/implement-verification-logic"]
	vscode-merge-base = origin/develop
	remote = origin
	merge = refs/heads/feature/DCPF-51178/implement-verification-logic
[branch "feature/DCPF-51178/implement-verification-logic-fix-lint"]
	vscode-merge-base = origin/develop
[branch "refactor/DCPF-50258/fix-eslint-warning"]
	remote = origin
	merge = refs/heads/refactor/DCPF-50258/fix-eslint-warning
	vscode-merge-base = origin/develop
[branch "feature/DCPF-50257/implement-migration-logic"]
	vscode-merge-base = origin/develop
[branch "fix/DCPF-50258/migration-error"]
	remote = origin
	merge = refs/heads/fix/DCPF-50258/migration-error
[branch "fix/DCPF-50982/migration-error"]
	remote = origin
	merge = refs/heads/fix/DCPF-50982/migration-error
[branch "feature/DCPF-51804/yuucho-integration-test"]
	remote = origin
	merge = refs/heads/feature/DCPF-51804/yuucho-integration-test
	vscode-merge-base = origin/develop
[branch "project/jpbank"]
	remote = origin
	merge = refs/heads/project/jpbank
	vscode-merge-base = origin/project/jpbank
[branch "fix/DCPF-52616/contract-release-codebuild"]
	remote = origin
	merge = refs/heads/fix/DCPF-52616/contract-release-codebuild
[branch "story/DCPF-51151/yucho-non-functional"]
	remote = origin
	merge = refs/heads/story/DCPF-51151/yucho-non-functional
[branch "feature/DCPF-49358"]
	remote = origin
	merge = refs/heads/feature/DCPF-49358
	vscode-merge-base = origin/feature/DCPF-49358
[branch "feature/DCPF-48538/issuer-external-storage-pattern-fix"]
	vscode-merge-base = origin/develop
[branch "feature/DCPF-52682/issuer-external-storage-pattern-deploy"]
	remote = origin
	merge = refs/heads/feature/DCPF-52682/issuer-external-storage-pattern-deploy
	vscode-merge-base = origin/develop
[diff]
	renames = true
